import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';
import 'add_product_page.dart';
import 'edit_product_page.dart';

class ProductsManagementPage extends StatefulWidget {
  const ProductsManagementPage({super.key});

  @override
  State<ProductsManagementPage> createState() => _ProductsManagementPageState();
}

class _ProductsManagementPageState extends State<ProductsManagementPage> {
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = '';

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    try {
      // محاولة جلب المنتجات من Firestore أولاً
      List<Product> products = await FirestoreDataService.getProducts();

      // إذا لم توجد منتجات في Firestore، استخدم البيانات المحلية
      if (products.isEmpty) {
        products = await ApiService.getProducts();
      }

      setState(() {
        _products = products;
        _filteredProducts = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المنتجات: $e')));
      }
    }
  }

  void _filterProducts() {
    setState(() {
      _filteredProducts = _products.where((product) {
        final matchesSearch =
            product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            product.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );
        final matchesCategory =
            _selectedCategory.isEmpty ||
            product.categoryId == _selectedCategory;
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  Future<void> _deleteProduct(Product product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.errorColor),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // حذف من Firestore أولاً
        await FirestoreDataService.deleteProduct(product.id);

        // ثم حذف من التخزين المحلي كنسخة احتياطية
        try {
          await ApiService.deleteProduct(product.id);
        } catch (e) {
          if (kDebugMode) {
            print('تحذير: فشل في حذف المنتج من التخزين المحلي: $e');
          }
        }

        await _loadProducts();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المنتج بنجاح من Firestore')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف المنتج: $e'),
              backgroundColor: AppColors.errorColor,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إدارة المنتجات'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const AddProductPage()),
              );
              if (result == true) {
                _loadProducts();
              }
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16),
            color: AppColors.white,
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في المنتجات...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _searchQuery = value;
                    _filterProducts();
                  },
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory.isEmpty
                            ? null
                            : _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'فلترة حسب الفئة',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: '',
                            child: Text('جميع الفئات'),
                          ),
                          ...['sunglasses', 'eyeglasses', 'contact-lenses'].map(
                            (category) => DropdownMenuItem(
                              value: category,
                              child: Text(_getCategoryName(category)),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          _selectedCategory = value ?? '';
                          _filterProducts();
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      onPressed: _loadProducts,
                      icon: const Icon(Icons.refresh),
                      label: const Text('تحديث'),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // قائمة المنتجات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredProducts.isEmpty
                ? const Center(
                    child: Text(
                      'لا توجد منتجات',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.secondaryText,
                      ),
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadProducts,
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredProducts.length,
                      itemBuilder: (context, index) {
                        final product = _filteredProducts[index];
                        return _buildProductCard(product);
                      },
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            product.image,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              width: 60,
              height: 60,
              color: AppColors.lightGray,
              child: const Icon(Icons.image_not_supported),
            ),
          ),
        ),
        title: Text(
          product.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getCategoryName(product.categoryId)),
            Text(
              '${product.price.toStringAsFixed(0)} د.ع',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            Row(
              children: [
                Icon(
                  product.inStock ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: product.inStock
                      ? AppColors.successColor
                      : AppColors.errorColor,
                ),
                const SizedBox(width: 4),
                Text(
                  product.inStock ? 'متوفر' : 'غير متوفر',
                  style: TextStyle(
                    fontSize: 12,
                    color: product.inStock
                        ? AppColors.successColor
                        : AppColors.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: AppColors.errorColor),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: AppColors.errorColor)),
                ],
              ),
            ),
          ],
          onSelected: (value) async {
            if (value == 'edit') {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => EditProductPage(product: product),
                ),
              );
              if (result == true) {
                _loadProducts();
              }
            } else if (value == 'delete') {
              _deleteProduct(product);
            }
          },
        ),
      ),
    );
  }

  String _getCategoryName(String categoryId) {
    switch (categoryId) {
      case 'sunglasses':
        return 'نظارات شمسية';
      case 'eyeglasses':
        return 'نظارات طبية';
      case 'contact-lenses':
        return 'عدسات لاصقة';
      case 'reading-glasses':
        return 'نظارات قراءة';
      default:
        return 'غير محدد';
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/data_service.dart';
import '../../services/firestore_data_service.dart';

class BrandsManagementPage extends StatefulWidget {
  const BrandsManagementPage({super.key});

  @override
  State<BrandsManagementPage> createState() => _BrandsManagementPageState();
}

class _BrandsManagementPageState extends State<BrandsManagementPage> {
  List<Map<String, dynamic>> _brands = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadBrands();
  }

  Future<void> _loadBrands() async {
    try {
      // جلب البراندات من Firestore أولاً
      List<Map<String, dynamic>> brandsData = [];
      try {
        brandsData = await FirestoreDataService.getBrands();
        if (kDebugMode) {
          print('✅ تم جلب ${brandsData.length} براند من Firestore');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل في جلب البراندات من Firestore: $e');
          print('🔄 التبديل للبيانات المحلية...');
        }
        // في حالة فشل Firestore، استخدم البيانات المحلية
        brandsData = await DataService.getBrands();
      }

      setState(() {
        _brands = brandsData;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل البراندات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إدارة البراندات'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddBrandDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadBrands,
              child: Column(
                children: [
                  _buildSearchBar(),
                  Expanded(
                    child: _filteredBrands.isEmpty
                        ? _buildEmptyState()
                        : _buildBrandsList(),
                  ),
                ],
              ),
            ),
    );
  }

  List<Map<String, dynamic>> get _filteredBrands {
    if (_searchQuery.isEmpty) {
      return _brands;
    }
    return _brands.where((brand) {
      final name = brand['name']?.toString().toLowerCase() ?? '';
      final description = brand['description']?.toString().toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();
      return name.contains(query) || description.contains(query);
    }).toList();
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: (value) => setState(() => _searchQuery = value),
        decoration: const InputDecoration(
          hintText: 'البحث في البراندات...',
          prefixIcon: Icon(Icons.search, color: AppColors.grey),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.branding_watermark_outlined,
            size: 80,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ? 'لا توجد براندات تطابق البحث' : 'لا توجد براندات',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.secondaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty 
                ? 'جرب البحث بكلمات مختلفة'
                : 'أضف براندات جديدة لتظهر هنا',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showAddBrandDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة براند جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBrandsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredBrands.length,
      itemBuilder: (context, index) {
        final brand = _filteredBrands[index];
        return _buildBrandCard(brand);
      },
    );
  }

  Widget _buildBrandCard(Map<String, dynamic> brand) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(
            Icons.branding_watermark,
            color: AppColors.primaryColor,
          ),
        ),
        title: Text(
          brand['name'] ?? '',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          brand['description'] ?? '',
          style: const TextStyle(
            color: AppColors.secondaryText,
            fontSize: 14,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditBrandDialog(brand);
                break;
              case 'delete':
                _confirmDeleteBrand(brand);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: AppColors.primaryColor),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: AppColors.errorColor),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showAddBrandDialog() async {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة براند جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم البراند',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف البراند',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.white,
            ),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );

    if (result == true && nameController.text.isNotEmpty) {
      await _addBrand(nameController.text, descriptionController.text);
    }
  }

  Future<void> _showEditBrandDialog(Map<String, dynamic> brand) async {
    final nameController = TextEditingController(text: brand['name']);
    final descriptionController = TextEditingController(text: brand['description']);

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل البراند'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم البراند',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف البراند',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.white,
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result == true) {
      await _updateBrand(brand['id'], nameController.text, descriptionController.text);
    }
  }

  Future<void> _addBrand(String name, String description) async {
    try {
      final brandData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'name': name,
        'description': description,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await FirestoreDataService.addBrand(brandData);
      await _loadBrands();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة البراند بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة البراند: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _updateBrand(String id, String name, String description) async {
    try {
      final brandData = {
        'id': id,
        'name': name,
        'description': description,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await FirestoreDataService.updateBrand(brandData);
      await _loadBrands();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البراند بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث البراند: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _confirmDeleteBrand(Map<String, dynamic> brand) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف البراند "${brand['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
              foregroundColor: AppColors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteBrand(brand['id']);
    }
  }

  Future<void> _deleteBrand(String id) async {
    try {
      await FirestoreDataService.deleteBrand(id);
      await _loadBrands();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف البراند بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف البراند: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }
}

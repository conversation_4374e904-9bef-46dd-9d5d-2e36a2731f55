import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/product.dart';
import '../models/category.dart' as app_category;
import '../models/user_simple.dart' as user_model;

// نموذج عنصر السلة
class CartItem {
  final String id;
  final Product product;
  int quantity;
  final String? selectedColor;
  final String? selectedSize;

  CartItem({
    required this.id,
    required this.product,
    this.quantity = 1,
    this.selectedColor,
    this.selectedSize,
  });

  double get totalPrice => product.price * quantity;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      'selectedColor': selectedColor,
      'selectedSize': selectedSize,
    };
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'],
      product: Product.fromJson(json['product']),
      quantity: json['quantity'],
      selectedColor: json['selectedColor'],
      selectedSize: json['selectedSize'],
    );
  }
}

// نموذج المفضلة
class WishlistItem {
  final String id;
  final Product product;
  final DateTime addedAt;

  WishlistItem({
    required this.id,
    required this.product,
    required this.addedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory WishlistItem.fromJson(Map<String, dynamic> json) {
    return WishlistItem(
      id: json['id'],
      product: Product.fromJson(json['product']),
      addedAt: DateTime.parse(json['addedAt']),
    );
  }
}

// إدارة حالة التطبيق
class AppState extends ChangeNotifier {
  static final AppState _instance = AppState._internal();
  factory AppState() => _instance;
  AppState._internal();

  // المستخدم الحالي
  user_model.User? _currentUser;
  user_model.User? get currentUser => _currentUser;

  // السلة
  final List<CartItem> _cartItems = [];
  List<CartItem> get cartItems => List.unmodifiable(_cartItems);

  // المفضلة
  final List<WishlistItem> _wishlistItems = [];
  List<WishlistItem> get wishlistItems => List.unmodifiable(_wishlistItems);

  // المنتجات
  List<Product> _products = [];
  List<Product> get products => List.unmodifiable(_products);

  // الفئات
  List<app_category.Category> _categories = [];
  List<app_category.Category> get categories => List.unmodifiable(_categories);

  // فلاتر البحث
  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  String _selectedCategory = '';
  String get selectedCategory => _selectedCategory;

  final List<String> _selectedFilters = [];
  List<String> get selectedFilters => List.unmodifiable(_selectedFilters);

  // حالة التحميل
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // تسجيل دخول المستخدم
  void setCurrentUser(user_model.User? user) {
    _currentUser = user;
    _saveUserState();
    notifyListeners();
  }

  // حفظ حالة المستخدم في التخزين المحلي
  Future<void> _saveUserState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUser != null) {
        final userJson = jsonEncode(_currentUser!.toJson());
        await prefs.setString('current_user', userJson);
      } else {
        await prefs.remove('current_user');
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ حالة المستخدم: $e');
    }
  }

  // تحميل حالة المستخدم من التخزين المحلي
  Future<void> loadUserState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        _currentUser = user_model.User.fromJson(userMap);
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل حالة المستخدم: $e');
    }
  }

  // إدارة السلة
  void addToCart(
    Product product, {
    String? color,
    String? size,
    int quantity = 1,
  }) {
    final existingIndex = _cartItems.indexWhere(
      (item) =>
          item.product.id == product.id &&
          item.selectedColor == color &&
          item.selectedSize == size,
    );

    if (existingIndex >= 0) {
      _cartItems[existingIndex].quantity += quantity;
    } else {
      _cartItems.add(
        CartItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          product: product,
          quantity: quantity,
          selectedColor: color,
          selectedSize: size,
        ),
      );
    }
    notifyListeners();
  }

  // إضافة عنصر سلة مباشرة
  void addCartItem(CartItem cartItem) {
    final existingIndex = _cartItems.indexWhere(
      (item) =>
          item.product.id == cartItem.product.id &&
          item.selectedColor == cartItem.selectedColor &&
          item.selectedSize == cartItem.selectedSize,
    );

    if (existingIndex >= 0) {
      _cartItems[existingIndex].quantity += cartItem.quantity;
    } else {
      _cartItems.add(cartItem);
    }
    notifyListeners();
  }

  void removeFromCart(String cartItemId) {
    _cartItems.removeWhere((item) => item.id == cartItemId);
    notifyListeners();
  }

  void updateCartItemQuantity(String cartItemId, int quantity) {
    final index = _cartItems.indexWhere((item) => item.id == cartItemId);
    if (index >= 0) {
      if (quantity <= 0) {
        _cartItems.removeAt(index);
      } else {
        _cartItems[index].quantity = quantity;
      }
      notifyListeners();
    }
  }

  void clearCart() {
    _cartItems.clear();
    notifyListeners();
  }

  // حساب إجمالي السلة
  double get cartSubtotal {
    return _cartItems.fold(0, (sum, item) => sum + item.totalPrice);
  }

  double get cartShipping {
    return cartSubtotal > 200000
        ? 0
        : 10000.0; // شحن مجاني للطلبات أكثر من 200 ألف دينار
  }

  double get cartTotal {
    return cartSubtotal + cartShipping;
  }

  int get cartItemsCount {
    return _cartItems.fold(0, (sum, item) => sum + item.quantity);
  }

  // إدارة المفضلة
  void addToWishlist(Product product) {
    if (!isInWishlist(product.id)) {
      _wishlistItems.add(
        WishlistItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          product: product,
          addedAt: DateTime.now(),
        ),
      );
      notifyListeners();
    }
  }

  void removeFromWishlist(String productId) {
    _wishlistItems.removeWhere((item) => item.product.id == productId);
    notifyListeners();
  }

  // مسح المفضلة
  void clearWishlist() {
    _wishlistItems.clear();
    notifyListeners();
  }

  bool isInWishlist(String productId) {
    return _wishlistItems.any((item) => item.product.id == productId);
  }

  void toggleWishlist(Product product) {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  }

  // إدارة المنتجات والفئات
  void setProducts(List<Product> products) {
    _products = products;
    notifyListeners();
  }

  void setCategories(List<app_category.Category> categories) {
    _categories = categories;
    notifyListeners();
  }

  // البحث والفلترة
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setSelectedCategory(String categoryId) {
    _selectedCategory = categoryId;
    notifyListeners();
  }

  void toggleFilter(String filter) {
    if (_selectedFilters.contains(filter)) {
      _selectedFilters.remove(filter);
    } else {
      _selectedFilters.add(filter);
    }
    notifyListeners();
  }

  void clearFilters() {
    _selectedFilters.clear();
    _selectedCategory = '';
    _searchQuery = '';
    notifyListeners();
  }

  // فلترة المنتجات
  List<Product> getFilteredProducts() {
    List<Product> filtered = List.from(_products);

    // فلترة بالبحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (product) =>
                product.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                product.description.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ),
          )
          .toList();
    }

    // فلترة بالفئة
    if (_selectedCategory.isNotEmpty) {
      filtered = filtered
          .where((product) => product.categoryId == _selectedCategory)
          .toList();
    }

    // فلترة بالفلاتر المحددة
    for (String filter in _selectedFilters) {
      switch (filter) {
        case 'نظارات طبية':
          filtered = filtered
              .where((product) => product.categoryId == 'medical_glasses')
              .toList();
          break;
        case 'نظارات شمسية':
          filtered = filtered
              .where((product) => product.categoryId == 'sunglasses')
              .toList();
          break;
        case 'عدسات لاصقة':
          filtered = filtered
              .where((product) => product.categoryId == 'contact_lenses')
              .toList();
          break;
        case 'إكسسوارات':
          filtered = filtered
              .where((product) => product.categoryId == 'accessories')
              .toList();
          break;
      }
    }

    return filtered;
  }

  // حالة التحميل
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تسجيل الخروج
  Future<void> logout() async {
    _currentUser = null;
    _cartItems.clear();
    _wishlistItems.clear();
    clearFilters();

    // مسح حالة المستخدم من التخزين المحلي
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
    } catch (e) {
      if (kDebugMode) print('خطأ في مسح حالة المستخدم: $e');
    }

    notifyListeners();
  }
}

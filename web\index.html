<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDvOiEU9Q8W7X2Y3Z4A5B6C7D8E9F0G1H2",
      authDomain: "visionlens-app-real.firebaseapp.com",
      projectId: "visionlens-app-real",
      storageBucket: "visionlens-app-real.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:abcdef************",
      measurementId: "G-ABCDEF1234"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    console.log('🔥 Firebase initialized successfully');
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>

import 'package:flutter/foundation.dart';
import '../models/user_simple.dart' as user_model;
import 'firebase_mock_service.dart';

/// خدمة المصادقة المحاكية
class AuthService {
  // الحصول على المستخدم الحالي
  static user_model.User? get currentUser =>
      FirebaseMockService.getCurrentUser();

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google
  static Future<AuthResult> signInWithGoogle() async {
    try {
      final result = await FirebaseMockService.signInWithGoogle();

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(
          result.errorMessage ?? 'فشل في تسجيل الدخول بـ Google',
        );
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ في تسجيل الدخول بـ Google: $e');
    }
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final result = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      return await FirebaseMockService.signOut();
    } catch (e) {
      if (kDebugMode) print('خطأ في تسجيل الخروج: $e');
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني (محاكاة)
  static Future<bool> sendEmailVerification() async {
    try {
      // محاكاة إرسال رسالة التأكيد
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في إرسال رسالة التأكيد: $e');
      return false;
    }
  }

  /// إعادة تعيين كلمة المرور (محاكاة)
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      // محاكاة إرسال رسالة إعادة التعيين
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في إرسال رسالة إعادة التعيين: $e');
      return false;
    }
  }

  /// تغيير كلمة المرور (محاكاة)
  static Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // محاكاة تغيير كلمة المرور
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في تغيير كلمة المرور: $e');
      return false;
    }
  }

  /// تحديث البريد الإلكتروني (محاكاة)
  static Future<bool> updateEmail(String newEmail) async {
    try {
      // محاكاة تحديث البريد الإلكتروني
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث البريد الإلكتروني: $e');
      return false;
    }
  }

  /// حذف الحساب (محاكاة)
  static Future<bool> deleteAccount(String password) async {
    try {
      // محاكاة حذف الحساب
      await Future.delayed(const Duration(seconds: 1));
      await signOut();
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في حذف الحساب: $e');
      return false;
    }
  }

  // تم حذف دالة _getErrorMessage لأنها غير مستخدمة
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}

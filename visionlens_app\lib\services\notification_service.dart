import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/notification.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static final List<AppNotification> _notificationsList = [];
  static final ValueNotifier<List<AppNotification>> notificationsNotifier =
      ValueNotifier([]);

  // تهيئة الإشعارات
  static Future<void> initialize() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // إضافة إشعارات تجريبية
    _addSampleNotifications();
  }

  // معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    // يمكن إضافة منطق التنقل هنا
    if (kDebugMode) {
      print('تم النقر على الإشعار: ${response.payload}');
    }
  }

  // إضافة إشعار جديد
  static void addNotification(AppNotification notification) {
    _notificationsList.insert(0, notification);
    notificationsNotifier.value = List.from(_notificationsList);

    // عرض الإشعار المحلي
    _showLocalNotification(notification);
  }

  // عرض إشعار محلي
  static Future<void> _showLocalNotification(
    AppNotification notification,
  ) async {
    const androidDetails = AndroidNotificationDetails(
      'visionlens_channel',
      'VisionLens Notifications',
      channelDescription: 'إشعارات تطبيق VisionLens',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      notification.id.hashCode,
      notification.title,
      notification.body,
      details,
      payload: notification.id,
    );
  }

  // الحصول على جميع الإشعارات
  static List<AppNotification> getAllNotifications() {
    return List.from(_notificationsList);
  }

  // الحصول على الإشعارات غير المقروءة
  static List<AppNotification> getUnreadNotifications() {
    return _notificationsList.where((n) => !n.isRead).toList();
  }

  // عدد الإشعارات غير المقروءة
  static int getUnreadCount() {
    return _notificationsList.where((n) => !n.isRead).length;
  }

  // تحديد إشعار كمقروء
  static void markAsRead(String notificationId) {
    final index = _notificationsList.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notificationsList[index] = _notificationsList[index].copyWith(
        isRead: true,
      );
      notificationsNotifier.value = List.from(_notificationsList);
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  static void markAllAsRead() {
    for (int i = 0; i < _notificationsList.length; i++) {
      _notificationsList[i] = _notificationsList[i].copyWith(isRead: true);
    }
    notificationsNotifier.value = List.from(_notificationsList);
  }

  // حذف إشعار
  static void deleteNotification(String notificationId) {
    _notificationsList.removeWhere((n) => n.id == notificationId);
    notificationsNotifier.value = List.from(_notificationsList);
  }

  // مسح جميع الإشعارات
  static void clearAllNotifications() {
    _notificationsList.clear();
    notificationsNotifier.value = [];
  }

  // إضافة إشعارات تجريبية
  static void _addSampleNotifications() {
    final sampleNotifications = [
      AppNotification(
        id: '1',
        title: 'مرحباً بك في VisionLens!',
        body: 'اكتشف أحدث مجموعة من النظارات والعدسات اللاصقة',
        type: NotificationType.welcome,
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        isRead: false,
      ),
      AppNotification(
        id: '2',
        title: 'عرض خاص!',
        body: 'خصم 25% على جميع النظارات الشمسية لفترة محدودة',
        type: NotificationType.promotion,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: false,
        actionUrl: '/products?category=sunglasses',
      ),
      AppNotification(
        id: '3',
        title: 'تم شحن طلبك',
        body: 'طلبك #ORD001 في الطريق إليك. متوقع الوصول خلال 2-3 أيام',
        type: NotificationType.orderUpdate,
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        isRead: true,
        actionUrl: '/orders/ORD001',
      ),
      AppNotification(
        id: '4',
        title: 'منتجات جديدة!',
        body: 'تم إضافة مجموعة جديدة من العدسات اللاصقة الملونة',
        type: NotificationType.newProduct,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isRead: false,
        actionUrl: '/products?category=colored-lenses',
      ),
      AppNotification(
        id: '5',
        title: 'تذكير الفحص الدوري',
        body: 'حان وقت فحص النظر الدوري. احجز موعدك الآن',
        type: NotificationType.reminder,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        isRead: false,
        actionUrl: '/book-appointment',
      ),
    ];

    _notificationsList.addAll(sampleNotifications);
    notificationsNotifier.value = List.from(_notificationsList);
  }

  // إشعارات خاصة بالطلبات
  static void notifyOrderStatusUpdate(String orderId, String status) {
    String title = '';
    String body = '';

    switch (status.toLowerCase()) {
      case 'confirmed':
        title = 'تم تأكيد طلبك';
        body = 'طلبك #$orderId تم تأكيده وسيتم تحضيره قريباً';
        break;
      case 'preparing':
        title = 'جاري تحضير طلبك';
        body = 'طلبك #$orderId قيد التحضير';
        break;
      case 'shipped':
        title = 'تم شحن طلبك';
        body = 'طلبك #$orderId في الطريق إليك';
        break;
      case 'delivered':
        title = 'تم تسليم طلبك';
        body = 'طلبك #$orderId تم تسليمه بنجاح';
        break;
      default:
        title = 'تحديث حالة الطلب';
        body = 'تم تحديث حالة طلبك #$orderId';
    }

    addNotification(
      AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        body: body,
        type: NotificationType.orderUpdate,
        createdAt: DateTime.now(),
        isRead: false,
        actionUrl: '/orders/$orderId',
      ),
    );
  }

  // إشعار العروض الخاصة
  static void notifySpecialOffer(
    String title,
    String description,
    String? actionUrl,
  ) {
    addNotification(
      AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        body: description,
        type: NotificationType.promotion,
        createdAt: DateTime.now(),
        isRead: false,
        actionUrl: actionUrl,
      ),
    );
  }

  // إشعار المنتجات الجديدة
  static void notifyNewProduct(String productName, String category) {
    addNotification(
      AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: 'منتج جديد!',
        body: 'تم إضافة $productName إلى فئة $category',
        type: NotificationType.newProduct,
        createdAt: DateTime.now(),
        isRead: false,
        actionUrl: '/products?new=true',
      ),
    );
  }

  // إشعار التذكيرات
  static void notifyReminder(String title, String message, String? actionUrl) {
    addNotification(
      AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        body: message,
        type: NotificationType.reminder,
        createdAt: DateTime.now(),
        isRead: false,
        actionUrl: actionUrl,
      ),
    );
  }

  // طلب إذن الإشعارات
  static Future<bool> requestPermission() async {
    final result = await _notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.requestNotificationsPermission();
    return result ?? false;
  }

  // إلغاء جميع الإشعارات المحلية
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }
}

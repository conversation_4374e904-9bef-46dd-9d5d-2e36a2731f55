import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../models/user_simple.dart' as user_model;
import '../models/order.dart' as order_model;
import '../models/category.dart' as category_model;

/// خدمة Firebase محاكية للتطوير والاختبار
class FirebaseMockService {
  static final Map<String, dynamic> _mockDatabase = {};
  static final Map<String, user_model.User> _users = {};
  static user_model.User? _currentUser;
  static final Random _random = Random();

  // تهيئة البيانات التجريبية
  static Future<void> initialize() async {
    await _initializeMockData();
    if (kDebugMode) print('Firebase Mock Service تم تهيئته بنجاح');
  }

  // ==================== المصادقة ====================

  static Future<MockAuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    await _simulateNetworkDelay();

    // محاكاة التحقق من البيانات
    if (email.isEmpty || password.isEmpty) {
      return MockAuthResult.failure('البريد الإلكتروني وكلمة المرور مطلوبان');
    }

    if (!_isValidEmail(email)) {
      return MockAuthResult.failure('البريد الإلكتروني غير صحيح');
    }

    if (password.length < 6) {
      return MockAuthResult.failure('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    // البحث عن المستخدم أو إنشاء واحد جديد
    user_model.User? user = _users[email];
    if (user == null) {
      // إنشاء مستخدم جديد مع تحديد الدور
      user_model.UserRole role = user_model.UserRole.customer;

      // جعل بعض الإيميلات مدراء
      if (email == '<EMAIL>' ||
          email == '<EMAIL>' ||
          email.contains('admin')) {
        role = user_model.UserRole.admin;
      }

      user = user_model.User(
        id: _generateId(),
        email: email,
        firstName: role == user_model.UserRole.admin ? 'مدير' : 'مستخدم',
        lastName: role == user_model.UserRole.admin ? 'النظام' : 'جديد',
        isEmailVerified: true,
        role: role,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      _users[email] = user;
    }

    _currentUser = user;
    return MockAuthResult.success(user);
  }

  static Future<MockAuthResult> signInWithGoogle() async {
    await _simulateNetworkDelay();

    // محاكاة تسجيل الدخول بـ Google (للاستخدام الداخلي فقط)
    final email = 'user${_random.nextInt(1000)}@gmail.com';
    final user = user_model.User(
      id: _generateId(),
      email: email,
      firstName: 'مستخدم',
      lastName: 'Google',
      isEmailVerified: true,
      role: user_model.UserRole.customer,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _users[email] = user;
    _currentUser = user;
    return MockAuthResult.success(user);
  }

  /// حفظ مستخدم Google الحقيقي في النظام المحاكي
  static Future<void> saveGoogleUser(
    user_model.User user,
    Map<String, dynamic> googleData,
  ) async {
    await _simulateNetworkDelay();

    // حفظ المستخدم
    _users[user.email] = user;
    _currentUser = user;

    // حفظ بيانات Google الإضافية
    _mockDatabase['google_users'] ??= <String, dynamic>{};
    _mockDatabase['google_users'][user.id] = {
      ...googleData,
      'savedAt': DateTime.now().toIso8601String(),
    };

    if (kDebugMode) print('✅ تم حفظ مستخدم Google: ${user.email}');
    if (kDebugMode) {
      print('📊 بيانات Google المحفوظة: ${googleData.keys.join(', ')}');
    }
  }

  static Future<MockAuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    await _simulateNetworkDelay();

    if (_users.containsKey(email)) {
      return MockAuthResult.failure('البريد الإلكتروني مستخدم بالفعل');
    }

    final user = user_model.User(
      id: _generateId(),
      email: email,
      firstName: firstName,
      lastName: lastName,
      phone: phone,
      isEmailVerified: false,
      role: user_model
          .UserRole
          .customer, // المستخدمون الجدد عملاء افتراض<|im_start|>
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _users[email] = user;
    _currentUser = user;
    return MockAuthResult.success(user);
  }

  static Future<bool> signOut() async {
    await _simulateNetworkDelay();
    _currentUser = null;
    return true;
  }

  static user_model.User? getCurrentUser() {
    return _currentUser;
  }

  static bool get isLoggedIn => _currentUser != null;

  // ==================== المنتجات ====================

  static Future<List<Product>> getAllProducts() async {
    await _simulateNetworkDelay();
    final products = _mockDatabase['products'] as List<dynamic>? ?? [];
    return products.map((p) => Product.fromJson(p)).toList();
  }

  static Future<Product?> getProductById(String productId) async {
    await _simulateNetworkDelay();
    final products = await getAllProducts();
    try {
      return products.firstWhere((p) => p.id == productId);
    } catch (e) {
      return null;
    }
  }

  static Future<List<Product>> searchProducts(String query) async {
    await _simulateNetworkDelay();
    final products = await getAllProducts();
    final lowercaseQuery = query.toLowerCase();

    return products.where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
          product.description.toLowerCase().contains(lowercaseQuery) ||
          (product.brand?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    await _simulateNetworkDelay();
    final products = await getAllProducts();
    return products.where((p) => p.categoryId == categoryId).toList();
  }

  // ==================== الفئات ====================

  static Future<List<category_model.Category>> getAllCategories() async {
    await _simulateNetworkDelay();
    final categories = _mockDatabase['categories'] as List<dynamic>? ?? [];
    return categories.map((c) => category_model.Category.fromJson(c)).toList();
  }

  // ==================== السلة والمفضلة ====================

  static Future<List<Map<String, dynamic>>> getUserCart(String userId) async {
    await _simulateNetworkDelay();
    final userCarts = _mockDatabase['carts'] as Map<String, dynamic>? ?? {};
    return List<Map<String, dynamic>>.from(userCarts[userId] ?? []);
  }

  static Future<bool> addToCart(
    String userId,
    Map<String, dynamic> cartItem,
  ) async {
    await _simulateNetworkDelay();
    final userCarts = _mockDatabase['carts'] as Map<String, dynamic>? ?? {};
    final userCart = List<Map<String, dynamic>>.from(userCarts[userId] ?? []);

    cartItem['id'] = _generateId();
    cartItem['addedAt'] = DateTime.now().toIso8601String();
    userCart.add(cartItem);

    userCarts[userId] = userCart;
    _mockDatabase['carts'] = userCarts;
    return true;
  }

  static Future<bool> removeFromCart(String userId, String cartItemId) async {
    await _simulateNetworkDelay();
    final userCarts = _mockDatabase['carts'] as Map<String, dynamic>? ?? {};
    final userCart = List<Map<String, dynamic>>.from(userCarts[userId] ?? []);

    userCart.removeWhere((item) => item['id'] == cartItemId);
    userCarts[userId] = userCart;
    _mockDatabase['carts'] = userCarts;
    return true;
  }

  static Future<bool> clearCart(String userId) async {
    await _simulateNetworkDelay();
    final userCarts = _mockDatabase['carts'] as Map<String, dynamic>? ?? {};
    userCarts[userId] = [];
    _mockDatabase['carts'] = userCarts;
    return true;
  }

  static Future<List<Map<String, dynamic>>> getUserWishlist(
    String userId,
  ) async {
    await _simulateNetworkDelay();
    final userWishlists =
        _mockDatabase['wishlists'] as Map<String, dynamic>? ?? {};
    return List<Map<String, dynamic>>.from(userWishlists[userId] ?? []);
  }

  static Future<bool> addToWishlist(String userId, String productId) async {
    await _simulateNetworkDelay();
    final userWishlists =
        _mockDatabase['wishlists'] as Map<String, dynamic>? ?? {};
    final userWishlist = List<Map<String, dynamic>>.from(
      userWishlists[userId] ?? [],
    );

    // التحقق من عدم وجود المنتج مسبقاً
    if (!userWishlist.any((item) => item['productId'] == productId)) {
      userWishlist.add({
        'id': _generateId(),
        'productId': productId,
        'addedAt': DateTime.now().toIso8601String(),
      });

      userWishlists[userId] = userWishlist;
      _mockDatabase['wishlists'] = userWishlists;
    }
    return true;
  }

  static Future<bool> removeFromWishlist(
    String userId,
    String productId,
  ) async {
    await _simulateNetworkDelay();
    final userWishlists =
        _mockDatabase['wishlists'] as Map<String, dynamic>? ?? {};
    final userWishlist = List<Map<String, dynamic>>.from(
      userWishlists[userId] ?? [],
    );

    userWishlist.removeWhere((item) => item['productId'] == productId);
    userWishlists[userId] = userWishlist;
    _mockDatabase['wishlists'] = userWishlists;
    return true;
  }

  // ==================== الطلبات ====================

  static Future<String?> createOrder(order_model.Order order) async {
    await _simulateNetworkDelay();
    final orders = _mockDatabase['orders'] as List<dynamic>? ?? [];

    final orderData = order.toJson();
    orderData['id'] = _generateId();
    orderData['createdAt'] = DateTime.now().toIso8601String();

    orders.add(orderData);
    _mockDatabase['orders'] = orders;

    return orderData['id'];
  }

  static Future<List<order_model.Order>> getUserOrders(String userId) async {
    await _simulateNetworkDelay();
    final orders = _mockDatabase['orders'] as List<dynamic>? ?? [];

    final userOrders = orders
        .where((order) => order['userId'] == userId)
        .map((order) => order_model.Order.fromJson(order))
        .toList();

    // ترتيب حسب التاريخ
    userOrders.sort((a, b) => b.date.compareTo(a.date));
    return userOrders;
  }

  // ==================== المساعدة ====================

  static Future<void> _simulateNetworkDelay() async {
    // محاكاة تأخير الشبكة
    await Future.delayed(Duration(milliseconds: 200 + _random.nextInt(300)));
  }

  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        _random.nextInt(1000).toString();
  }

  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static Future<void> _initializeMockData() async {
    // تهيئة الفئات
    _mockDatabase['categories'] = [
      {
        'id': 'sunglasses',
        'name': 'نظارات شمسية',
        'icon': 'sunglasses',
        'description': 'نظارات شمسية عصرية وأنيقة',
        'isActive': true,
        'order': 1,
      },
      {
        'id': 'eyeglasses',
        'name': 'نظارات طبية',
        'icon': 'eyeglasses',
        'description': 'نظارات طبية بأحدث التصاميم',
        'isActive': true,
        'order': 2,
      },
      {
        'id': 'contact-lenses',
        'name': 'عدسات لاصقة',
        'icon': 'contact_lens',
        'description': 'عدسات لاصقة مريحة وآمنة',
        'isActive': true,
        'order': 3,
      },
    ];

    // تهيئة المنتجات
    _mockDatabase['products'] = [
      {
        'id': 'prod_001',
        'name': 'نظارة Ray-Ban الكلاسيكية',
        'description':
            'نظارة شمسية كلاسيكية من Ray-Ban بتصميم أنيق ومقاوم للخدش',
        'price': 150000.0,
        'originalPrice': 180000.0,
        'categoryId': 'sunglasses',
        'brand': 'Ray-Ban',
        'images': [
          'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400',
          'https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=400',
        ],
        'rating': 4.5,
        'reviewCount': 128,
        'stock': 25,
        'isActive': true,
        'keywords': ['نظارة', 'شمسية', 'راي بان', 'كلاسيكية'],
        'colors': ['أسود', 'بني', 'ذهبي'],
        'sizes': ['صغير', 'متوسط', 'كبير'],
      },
      {
        'id': 'prod_002',
        'name': 'عدسات لاصقة يومية',
        'description': 'عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي',
        'price': 42500.0,
        'categoryId': 'contact-lenses',
        'brand': 'Acuvue',
        'images': [
          'https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400',
        ],
        'rating': 4.2,
        'reviewCount': 89,
        'stock': 50,
        'isActive': true,
        'keywords': ['عدسات', 'لاصقة', 'يومية', 'مريحة'],
        'colors': ['شفاف'],
        'sizes': ['8.4', '8.8', '9.0'],
      },
      {
        'id': 'prod_003',
        'name': 'نظارة Oakley الرياضية',
        'description': 'نظارة رياضية من Oakley مصممة للأنشطة الرياضية',
        'price': 120000.0,
        'categoryId': 'sunglasses',
        'brand': 'Oakley',
        'images': [
          'https://images.unsplash.com/photo-1508296695146-257a814070b4?w=400',
        ],
        'rating': 4.7,
        'reviewCount': 156,
        'stock': 15,
        'isActive': true,
        'keywords': ['نظارة', 'رياضية', 'أوكلي', 'متينة'],
        'colors': ['أسود', 'أزرق', 'أحمر'],
        'sizes': ['متوسط', 'كبير'],
      },
    ];

    // تهيئة البيانات الفارغة للمستخدمين
    _mockDatabase['carts'] = <String, dynamic>{};
    _mockDatabase['wishlists'] = <String, dynamic>{};
    _mockDatabase['orders'] = <dynamic>[];
  }

  // إعادة تعيين البيانات (للاختبار)
  static void reset() {
    _mockDatabase.clear();
    _users.clear();
    _currentUser = null;
  }

  // الحصول على إحصائيات
  static Map<String, dynamic> getStats() {
    return {
      'users': _users.length,
      'products': (_mockDatabase['products'] as List?)?.length ?? 0,
      'categories': (_mockDatabase['categories'] as List?)?.length ?? 0,
      'orders': (_mockDatabase['orders'] as List?)?.length ?? 0,
    };
  }
}

// نتيجة المصادقة المحاكية
class MockAuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  MockAuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  MockAuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}

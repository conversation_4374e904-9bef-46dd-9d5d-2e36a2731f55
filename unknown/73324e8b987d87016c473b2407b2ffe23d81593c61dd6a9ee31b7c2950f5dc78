name: connectivity_plus
description: Flutter plugin for discovering the state of the network (WiFi & mobile/cellular) connectivity on Android and iOS.
version: 5.0.2
homepage: https://plus.fluttercommunity.dev/
repository: https://github.com/fluttercommunity/plus_plugins/tree/main/packages/connectivity_plus/connectivity_plus
issue_tracker: https://github.com/fluttercommunity/plus_plugins/labels/connectivity_plus

environment:
  sdk: ">=2.18.0 <4.0.0"
  flutter: ">=3.3.0"

flutter:
  plugin:
    platforms:
      android:
        package: dev.fluttercommunity.plus.connectivity
        pluginClass: ConnectivityPlugin
      ios:
        pluginClass: ConnectivityPlusPlugin
      linux:
        dartPluginClass: ConnectivityPlusLinuxPlugin
      macos:
        pluginClass: ConnectivityPlugin
      web:
        pluginClass: ConnectivityPlusWebPlugin
        fileName: src/connectivity_plus_web.dart
      windows:
        pluginClass: ConnectivityPlusWindowsPlugin

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  connectivity_plus_platform_interface: ^1.2.4
  js: ^0.6.4
  meta: ^1.8.0
  nm: ^0.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.3.3
  dbus: ^0.7.8
  flutter_lints: ">=2.0.2 <4.0.0"
  mockito: ^5.4.0
  plugin_platform_interface: ^2.1.5
  test: ^1.22.0

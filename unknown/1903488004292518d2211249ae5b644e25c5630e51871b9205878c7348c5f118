rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد مؤقتة للتطوير - تسمح بالقراءة والكتابة للجميع
    // يجب تحديثها قبل الإنتاج
    match /{document=**} {
      allow read, write: if true;
    }

    // قواعد خاصة للمنتجات
    match /products/{productId} {
      allow read, write, delete: if true;
    }

    // قواعد خاصة للفئات
    match /categories/{categoryId} {
      allow read, write, delete: if true;
    }

    // قواعد خاصة للمستخدمين
    match /users/{userId} {
      allow read, write, delete: if true;
    }

    // قواعد خاصة للطلبات
    match /orders/{orderId} {
      allow read, write, delete: if true;
    }
  }
}
@echo off
echo 🔥 إعداد Firebase لتطبيق VisionLens...

echo.
echo 📦 تثبيت Firebase CLI...
call npm install -g firebase-tools

echo.
echo 🔑 تسجيل الدخول إلى Firebase...
call firebase login

echo.
echo 🏗️ تهيئة مشروع Firebase...
call firebase init

echo.
echo 📋 الخطوات التالية:
echo 1. اختر "Hosting" و "Firestore" و "Storage"
echo 2. اختر مشروع Firebase موجود أو أنشئ جديد
echo 3. اختر "build/web" كمجلد النشر
echo 4. اختر "Yes" لإعداد SPA
echo 5. اختر "No" لعدم الكتابة فوق index.html

echo.
echo ✅ تم إعداد Firebase بنجاح!
echo 🚀 يمكنك الآن تشغيل deploy.bat للنشر

pause

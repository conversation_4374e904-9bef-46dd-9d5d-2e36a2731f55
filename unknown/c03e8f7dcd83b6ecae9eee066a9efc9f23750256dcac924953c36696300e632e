import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// خدمة التخزين الهجين - تدعم التخزين المحلي والسحابي
class HybridStorageService {
  static const String _keyPrefix = 'visionlens_';

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 تهيئة خدمة التخزين الهجين...');
      print('📱 التخزين المحلي: متاح');
      print('☁️ التخزين السحابي: معطل مؤقتاً');
      print('✅ تم تهيئة خدمة التخزين الهجين بنجاح');
    }
  }

  /// حفظ البيانات محلياً
  static Future<void> saveLocal(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data);
      await prefs.setString('$_keyPrefix$key', jsonString);

      if (kDebugMode) {
        print('💾 تم حفظ البيانات محلياً: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حفظ البيانات محلياً: $e');
      }
    }
  }

  /// قراءة البيانات محلياً
  static Future<dynamic> loadLocal(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('$_keyPrefix$key');

      if (jsonString != null) {
        return jsonDecode(jsonString);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في قراءة البيانات محلياً: $e');
      }
      return null;
    }
  }

  /// حذف البيانات محلياً
  static Future<void> deleteLocal(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_keyPrefix$key');

      if (kDebugMode) {
        print('🗑️ تم حذف البيانات محلياً: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف البيانات محلياً: $e');
      }
    }
  }

  /// مسح جميع البيانات المحلية
  static Future<void> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));

      for (final key in keys) {
        await prefs.remove(key);
      }

      if (kDebugMode) {
        print('🧹 تم مسح جميع البيانات المحلية');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مسح البيانات: $e');
      }
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/storage_service.dart';

class AdminAccountsPage extends StatefulWidget {
  const AdminAccountsPage({super.key});

  @override
  State<AdminAccountsPage> createState() => _AdminAccountsPageState();
}

class _AdminAccountsPageState extends State<AdminAccountsPage> {
  final AppState _appState = AppState();
  List<String> _adminEmails = [];
  bool _isLoading = true;
  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAdminAccounts();
  }

  Future<void> _loadAdminAccounts() async {
    try {
      // جلب قائمة الحسابات الإدارية المحفوظة
      final savedEmails = await StorageService.getAdminEmails();
      setState(() {
        _adminEmails = savedEmails;
        _isLoading = false;
      });

      if (kDebugMode) {
        print('✅ تم تحميل ${_adminEmails.length} حساب إداري');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الحسابات الإدارية: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إدارة الحسابات الإدارية'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildAddAccountSection(),
                Expanded(child: _buildAccountsList()),
              ],
            ),
    );
  }

  Widget _buildAddAccountSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إضافة حساب إداري جديد',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'البريد الإلكتروني',
              hintText: '<EMAIL>',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.email),
            ),
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _addAdminAccount,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('إضافة حساب إداري'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsList() {
    if (_adminEmails.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.admin_panel_settings_outlined,
              size: 80,
              color: AppColors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد حسابات إدارية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.secondaryText,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'أضف حسابات إدارية للسماح لمستخدمين آخرين بالوصول للوحة الإدارة',
              style: TextStyle(fontSize: 14, color: AppColors.secondaryText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _adminEmails.length,
      itemBuilder: (context, index) {
        final email = _adminEmails[index];
        return _buildAccountCard(email, index);
      },
    );
  }

  Widget _buildAccountCard(String email, int index) {
    final currentUser = _appState.currentUser;
    final isCurrentUser = currentUser?.email == email;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isCurrentUser
                ? AppColors.primaryColor.withValues(alpha: 0.1)
                : AppColors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            isCurrentUser ? Icons.person : Icons.admin_panel_settings,
            color: isCurrentUser ? AppColors.primaryColor : AppColors.grey,
          ),
        ),
        title: Text(
          email,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        ),
        subtitle: Text(
          isCurrentUser ? 'أنت (المدير الحالي)' : 'مدير',
          style: TextStyle(
            color: isCurrentUser
                ? AppColors.primaryColor
                : AppColors.secondaryText,
            fontSize: 14,
          ),
        ),
        trailing: isCurrentUser
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'أنت',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
            : IconButton(
                icon: const Icon(Icons.delete, color: AppColors.errorColor),
                onPressed: () => _confirmDeleteAccount(email, index),
              ),
      ),
    );
  }

  Future<void> _addAdminAccount() async {
    // عرض تحذير أمني أولاً
    final confirmed = await _showSecurityWarning();
    if (!confirmed) return;

    final email = _emailController.text.trim();

    if (email.isEmpty) {
      _showMessage('يرجى إدخال البريد الإلكتروني', isError: true);
      return;
    }

    if (!_isValidEmail(email)) {
      _showMessage('يرجى إدخال بريد إلكتروني صحيح', isError: true);
      return;
    }

    if (_adminEmails.contains(email.toLowerCase())) {
      _showMessage('هذا البريد الإلكتروني موجود بالفعل', isError: true);
      return;
    }

    // التحقق من أن المستخدم لديه حساب Google مسجل
    final hasGoogleAccount = await _verifyGoogleAccount(email);
    if (!hasGoogleAccount) {
      _showMessage(
        'يجب أن يكون للمستخدم حساب Google مسجل مسبقاً',
        isError: true,
      );
      return;
    }

    try {
      setState(() {
        _adminEmails.add(email.toLowerCase());
      });

      await StorageService.saveAdminEmails(_adminEmails);
      _emailController.clear();

      _showMessage(
        'تم إضافة الحساب الإداري بنجاح - سيتمكن من الدخول عبر Google',
      );

      if (kDebugMode) {
        print('✅ تم إضافة حساب إداري: $email');
      }
    } catch (e) {
      setState(() {
        _adminEmails.removeLast();
      });

      _showMessage('خطأ في إضافة الحساب الإداري', isError: true);

      if (kDebugMode) {
        print('❌ خطأ في إضافة الحساب الإداري: $e');
      }
    }
  }

  Future<bool> _showSecurityWarning() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.security, color: AppColors.warningColor),
                SizedBox(width: 8),
                Text('تحذير أمني'),
              ],
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ملاحظات أمنية مهمة:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('• يجب أن يكون للمستخدم حساب Google صحيح'),
                Text('• سيتمكن من الوصول لجميع بيانات النظام'),
                Text('• تأكد من الثقة الكاملة في هذا المستخدم'),
                Text('• يمكن حذف الحساب لاحقاً إذا لزم الأمر'),
                SizedBox(height: 12),
                Text(
                  'هل أنت متأكد من إضافة هذا الحساب؟',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.warningColor,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warningColor,
                  foregroundColor: AppColors.white,
                ),
                child: const Text('متأكد، أضف الحساب'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<bool> _verifyGoogleAccount(String email) async {
    // محاكاة التحقق من وجود حساب Google
    // في التطبيق الحقيقي، يمكن التحقق من Firebase Auth
    await Future.delayed(const Duration(milliseconds: 500));

    // للتبسيط، نفترض أن جميع الإيميلات صحيحة
    // لكن في الواقع يجب التحقق من Firebase Auth
    return _isValidEmail(email);
  }

  Future<void> _confirmDeleteAccount(String email, int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحساب الإداري:\n$email'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
              foregroundColor: AppColors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteAdminAccount(index);
    }
  }

  Future<void> _deleteAdminAccount(int index) async {
    try {
      final email = _adminEmails[index];

      setState(() {
        _adminEmails.removeAt(index);
      });

      await StorageService.saveAdminEmails(_adminEmails);

      _showMessage('تم حذف الحساب الإداري بنجاح');

      if (kDebugMode) {
        print('✅ تم حذف حساب إداري: $email');
      }
    } catch (e) {
      await _loadAdminAccounts(); // إعادة تحميل البيانات

      _showMessage('خطأ في حذف الحساب الإداري', isError: true);

      if (kDebugMode) {
        print('❌ خطأ في حذف الحساب الإداري: $e');
      }
    }
  }

  void _showSecurityInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: AppColors.primaryColor),
            SizedBox(width: 8),
            Text('معلومات الأمان'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'كيف يعمل نظام الأمان:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('🔐 تسجيل الدخول عبر Google فقط'),
            Text('   • لا توجد كلمات مرور منفصلة'),
            Text('   • الأمان مضمون عبر Google'),
            SizedBox(height: 8),
            Text('👤 إضافة حسابات إدارية:'),
            Text('   • أدخل البريد الإلكتروني للمستخدم'),
            Text('   • يجب أن يكون له حساب Google'),
            Text('   • سيتمكن من الدخول فوراً'),
            SizedBox(height: 8),
            Text('🛡️ الأمان:'),
            Text('   • فقط الحسابات المضافة تصل للإدارة'),
            Text('   • يمكن حذف أي حساب في أي وقت'),
            Text('   • جميع العمليات مسجلة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.errorColor : AppColors.success,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }
}
